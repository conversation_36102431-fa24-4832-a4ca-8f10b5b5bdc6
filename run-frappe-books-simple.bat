@echo off
setlocal enabledelayedexpansion

:: Simple Frappe Books Runner (No Admin Required)
:: This script tries to run Frappe Books with existing tools or provides guidance

echo ==========================================
echo    Frappe Books Simple Runner
echo ==========================================
echo.

:: Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] package.json not found. 
    echo Make sure this script is in the Frappe Books directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo [INFO] Found package.json - we're in the right directory
echo.

:: Check for Node.js
echo [CHECK] Checking for Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found!
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Recommended: Download and install Node.js 18 LTS or 20 LTS
    echo.
    echo After installation:
    echo 1. Restart this command prompt
    echo 2. Run this script again
    pause
    exit /b 1
) else (
    echo [OK] Node.js found
    node --version
)

:: Check for package manager (prefer yarn, fallback to npm)
set "PKG_MANAGER="
echo [CHECK] Checking for Yarn...
where yarn >nul 2>&1
if %errorlevel% equ 0 (
    set "PKG_MANAGER=yarn"
    echo [OK] Yarn found
    yarn --version
) else (
    echo [INFO] Yarn not found, checking for npm...
    where npm >nul 2>&1
    if %errorlevel% equ 0 (
        set "PKG_MANAGER=npm"
        echo [OK] npm found
        npm --version
    ) else (
        echo [ERROR] Neither yarn nor npm found!
        echo npm should come with Node.js installation.
        echo Please reinstall Node.js from: https://nodejs.org/
        pause
        exit /b 1
    )
)

:: Check if dependencies are installed
if not exist "node_modules" (
    echo [INFO] Dependencies not installed. Installing now...
    goto :install_deps
)

:: Check if node_modules seems complete
if not exist "node_modules\electron" (
    echo [INFO] Dependencies seem incomplete. Reinstalling...
    goto :install_deps
)

echo [OK] Dependencies appear to be installed
goto :run_app

:install_deps
echo.
echo [INSTALL] Installing dependencies with %PKG_MANAGER%...
echo [INFO] This may take several minutes...
echo.

if "%PKG_MANAGER%"=="yarn" (
    yarn install
    set "INSTALL_RESULT=!errorlevel!"
) else (
    npm install
    set "INSTALL_RESULT=!errorlevel!"
)

if !INSTALL_RESULT! neq 0 (
    echo.
    echo [ERROR] Installation failed!
    echo.
    echo Common solutions:
    echo 1. Install Visual Studio Build Tools:
    echo    https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
    echo    - Select "C++ build tools" workload during installation
    echo.
    echo 2. Or install Visual Studio Community (includes build tools):
    echo    https://visualstudio.microsoft.com/vs/community/
    echo    - Select "Desktop development with C++" workload
    echo.
    echo 3. Try using Node.js 18 LTS instead of newer versions
    echo.
    echo 4. Run the full setup script as administrator:
    echo    Right-click "setup-and-run-frappe-books.bat" and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Dependencies installed successfully!

:run_app
echo.
echo [RUN] Starting Frappe Books...
echo [INFO] The application will open in a new Electron window
echo [INFO] Press Ctrl+C in this window to stop the application
echo.

if "%PKG_MANAGER%"=="yarn" (
    yarn dev
) else (
    npm run dev
)

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Failed to start Frappe Books
    echo.
    echo Troubleshooting:
    echo 1. Make sure all dependencies installed correctly
    echo 2. Check if any antivirus is blocking the application
    echo 3. Try running: yarn build (or npm run build) first
    echo.
    pause
    exit /b 1
)

echo.
echo [COMPLETE] Frappe Books session ended
pause
