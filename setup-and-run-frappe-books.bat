@echo off
setlocal enabledelayedexpansion

:: Frappe Books Auto Setup and Run Script
:: This script automatically installs all required dependencies and runs Frappe Books

echo ========================================
echo    Frappe Books Auto Setup Script
echo ========================================
echo.

:: Set colors for better output
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "RESET=%ESC%[0m"

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%[ERROR]%RESET% This script requires administrator privileges.
    echo %YELLOW%[INFO]%RESET% Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo %GREEN%[INFO]%RESET% Running with administrator privileges...
echo.

:: Function to check if a command exists
:check_command
where %1 >nul 2>&1
exit /b %errorlevel%

:: Function to download file
:download_file
echo %BLUE%[DOWNLOAD]%RESET% Downloading %2...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%1' -OutFile '%2'}"
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%RESET% Failed to download %2
    exit /b 1
)
echo %GREEN%[SUCCESS]%RESET% Downloaded %2
exit /b 0

:: Check and install Chocolatey
echo %BLUE%[CHECK]%RESET% Checking for Chocolatey...
call :check_command choco
if %errorlevel% neq 0 (
    echo %YELLOW%[INSTALL]%RESET% Installing Chocolatey...
    powershell -Command "& {Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))}"
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Chocolatey
        goto :error_exit
    )
    :: Refresh environment variables
    call refreshenv
    echo %GREEN%[SUCCESS]%RESET% Chocolatey installed successfully
) else (
    echo %GREEN%[OK]%RESET% Chocolatey is already installed
)

:: Check and install Node.js
echo %BLUE%[CHECK]%RESET% Checking for Node.js...
call :check_command node
if %errorlevel% neq 0 (
    echo %YELLOW%[INSTALL]%RESET% Installing Node.js LTS...
    choco install nodejs-lts -y
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Node.js
        goto :error_exit
    )
    call refreshenv
    echo %GREEN%[SUCCESS]%RESET% Node.js installed successfully
) else (
    echo %GREEN%[OK]%RESET% Node.js is already installed
    node --version
)

:: Check and install Yarn
echo %BLUE%[CHECK]%RESET% Checking for Yarn...
call :check_command yarn
if %errorlevel% neq 0 (
    echo %YELLOW%[INSTALL]%RESET% Installing Yarn...
    choco install yarn -y
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Yarn
        goto :error_exit
    )
    call refreshenv
    echo %GREEN%[SUCCESS]%RESET% Yarn installed successfully
) else (
    echo %GREEN%[OK]%RESET% Yarn is already installed
    yarn --version
)

:: Check and install Git
echo %BLUE%[CHECK]%RESET% Checking for Git...
call :check_command git
if %errorlevel% neq 0 (
    echo %YELLOW%[INSTALL]%RESET% Installing Git...
    choco install git -y
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Git
        goto :error_exit
    )
    call refreshenv
    echo %GREEN%[SUCCESS]%RESET% Git installed successfully
) else (
    echo %GREEN%[OK]%RESET% Git is already installed
)

:: Check for Visual Studio Build Tools
echo %BLUE%[CHECK]%RESET% Checking for Visual Studio Build Tools...
set "VS_FOUND=0"

:: Check for VS 2022 Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set "VS_FOUND=1"
    echo %GREEN%[OK]%RESET% Visual Studio 2022 Build Tools found
)

:: Check for VS 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set "VS_FOUND=1"
    echo %GREEN%[OK]%RESET% Visual Studio 2022 Community found
)

:: Check for VS 2019 Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set "VS_FOUND=1"
    echo %GREEN%[OK]%RESET% Visual Studio 2019 Build Tools found
)

if !VS_FOUND! equ 0 (
    echo %YELLOW%[INSTALL]%RESET% Installing Visual Studio Build Tools 2022...
    echo %BLUE%[INFO]%RESET% This may take several minutes...
    
    :: Download VS Build Tools installer
    set "VS_INSTALLER=%TEMP%\vs_buildtools.exe"
    call :download_file "https://aka.ms/vs/17/release/vs_buildtools.exe" "!VS_INSTALLER!"
    
    :: Install with C++ workload
    echo %BLUE%[INSTALL]%RESET% Installing C++ build tools...
    "!VS_INSTALLER!" --quiet --wait --add Microsoft.VisualStudio.Workload.VCTools --add Microsoft.VisualStudio.Component.VC.Tools.x86.x64 --add Microsoft.VisualStudio.Component.Windows11SDK.22000
    
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Visual Studio Build Tools
        echo %YELLOW%[INFO]%RESET% You may need to install manually from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
        goto :error_exit
    )
    
    :: Clean up installer
    del "!VS_INSTALLER!" >nul 2>&1
    echo %GREEN%[SUCCESS]%RESET% Visual Studio Build Tools installed successfully
)

echo.
echo %BLUE%[INFO]%RESET% All prerequisites installed. Setting up Frappe Books...
echo.

:: Navigate to the script directory
cd /d "%~dp0"

:: Check if we're in the right directory
if not exist "package.json" (
    echo %RED%[ERROR]%RESET% package.json not found. Make sure this script is in the Frappe Books directory.
    goto :error_exit
)

:: Clean previous installation if it failed
if exist "node_modules" (
    echo %YELLOW%[CLEANUP]%RESET% Removing previous node_modules...
    rmdir /s /q node_modules >nul 2>&1
    if exist "node_modules" (
        echo %YELLOW%[WARNING]%RESET% Some files are locked. Trying alternative cleanup...
        powershell -Command "Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue"
    )
)

:: Install dependencies
echo %BLUE%[INSTALL]%RESET% Installing Frappe Books dependencies...
echo %YELLOW%[INFO]%RESET% This may take several minutes for the first time...

yarn install
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%RESET% Yarn install failed. Trying with npm...
    npm install
    if !errorlevel! neq 0 (
        echo %RED%[ERROR]%RESET% Both yarn and npm install failed.
        echo %YELLOW%[SUGGESTION]%RESET% Try restarting your computer and running this script again.
        goto :error_exit
    )
)

echo %GREEN%[SUCCESS]%RESET% Dependencies installed successfully!
echo.

:: Run the application
echo %BLUE%[RUN]%RESET% Starting Frappe Books...
echo %YELLOW%[INFO]%RESET% The application will open in a new window.
echo %YELLOW%[INFO]%RESET% Press Ctrl+C in this window to stop the application.
echo.

yarn dev

goto :end

:error_exit
echo.
echo %RED%[FAILED]%RESET% Setup failed. Please check the errors above.
echo %YELLOW%[HELP]%RESET% For manual installation instructions, visit:
echo https://github.com/frappe/books#development-setup
pause
exit /b 1

:end
echo.
echo %GREEN%[COMPLETE]%RESET% Frappe Books setup and execution completed.
pause
