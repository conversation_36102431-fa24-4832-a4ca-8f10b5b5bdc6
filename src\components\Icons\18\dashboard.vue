<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
    <g fill="none" fill-rule="evenodd" transform="translate(.7)">
      <path
        :fill="lightColor"
        d="M6,9.33333333 L0.666666667,9.33333333 C0.298666667,9.33333333 1.95399252e-14,9.03466667 1.95399252e-14,8.66666667 L1.95399252e-14,0.666666667 C1.95399252e-14,0.298666667 0.298666667,0 0.666666667,0 L6,0 C6.368,0 6.66666667,0.298666667 6.66666667,0.666666667 L6.66666667,8.66666667 C6.66666667,9.03466667 6.368,9.33333333 6,9.33333333 Z M14,5.33333333 L8.66666667,5.33333333 C8.29866667,5.33333333 8,5.03466667 8,4.66666667 L8,0.666666667 C8,0.298666667 8.29866667,-4.4408921e-16 8.66666667,-4.4408921e-16 L14,-4.4408921e-16 C14.368,-4.4408921e-16 14.6666667,0.298666667 14.6666667,0.666666667 L14.6666667,4.66666667 C14.6666667,5.03466667 14.368,5.33333333 14,5.33333333 Z"
      />
      <path
        :fill="darkColor"
        fill-rule="nonzero"
        d="M6,16 L0.666666667,16 C0.298666667,16 1.95399252e-14,15.7013333 1.95399252e-14,15.3333333 L1.95399252e-14,11.3333333 C1.95399252e-14,10.9653333 0.298666667,10.6666667 0.666666667,10.6666667 L6,10.6666667 C6.368,10.6666667 6.66666667,10.9653333 6.66666667,11.3333333 L6.66666667,15.3333333 C6.66666667,15.7013333 6.368,16 6,16 Z M14,16 L8.66666667,16 C8.29866667,16 8,15.7013333 8,15.3333333 L8,7.33333333 C8,6.96533333 8.29866667,6.66666667 8.66666667,6.66666667 L14,6.66666667 C14.368,6.66666667 14.6666667,6.96533333 14.6666667,7.33333333 L14.6666667,15.3333333 C14.6666667,15.7013333 14.368,16 14,16 Z"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  name: 'IconDashboard',
  extends: Base,
};
</script>
