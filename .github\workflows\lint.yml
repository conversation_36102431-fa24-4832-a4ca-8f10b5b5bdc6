name: Lint

on:
  push:
    branches: [$default-branch]
    paths-ignore:
      - '**.md'
  pull_request:
    paths-ignore:
      - '**.md'
  workflow_dispatch:

jobs:
  setup_and_lint:
    runs-on: macos-latest
    steps:
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: '20.18.1'

      - name: Set yarn version
        run: yarn set version 1.22.18

      - name: Checkout Books
        uses: actions/checkout@v4

      - name: Install Dependencies
        run: yarn

      - name: Lint
        run: yarn lint

      - name: Check Formatting
        run: yarn prettier --check .
