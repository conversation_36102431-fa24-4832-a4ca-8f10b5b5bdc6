${0},,
"${0} ${1} already exists.","${0} ${1} 已经存在",
"${0} ${1} does not exist","${0} ${1} 不存在",
"${0} ${1} has been modified after loading please reload entry.","${0} ${1} 已在加载后修改，请重新加载条目",
"${0} ${1} is linked with existing records.","${0} ${1} 与现有记录相关联",
"${0} account not set in Inventory Settings.",${0}未在“库存设置”中设置帐户,
"${0} already saved","${0} 已保存",
"${0} already submitted","${0} 已提交",
"${0} cancelled","${0} 删除",
"${0} cannot be cancelled","${0} cannot be cancelled",
"${0} cannot be deleted","${0} 无法删除",
"${0} deleted","${0} 删除",
"${0} entries failed","${0} 条目失败",
"${0} entries imported","${0} 导入的条目",
"${0} entry failed","${0} 条目失败",
"${0} entry imported","已导入 ${0} 条目",
"${0} fields selected","已选择 ${0} 个字段",
"${0} filters applied","已应用 ${0} 个筛选器",
"${0} has linked child accounts.","${0} 已关联子帐户",
"${0} of type ${1} does not exist","类型为 ${1} 的 ${0} 不存在",
"${0} out of ${1}","${0} 共 ${1}",
"${0} party ${1} is different from ${2}","${0} 参与方 ${1} 与 ${2} 不同",
"${0} quantity 1 added.","添加了 ${0} 数量1",
"${0} row added.","已添加 ${0} 行",
"${0} rows","${0} 行",
"${0} rows added.","已添加 ${0} 行",
"${0} saved","已保存 ${0}",
"${0} shortcuts","${0} 快捷方式",
"${0} stored at ${1}","${0} 存储在 ${1}",
"${0} submitted","已提交 ${0}",
"${0} value ${1} does not exist.","${0} 值 ${1} 不存在",
0%,0%,
03-23-2022,03-23-2022,
03/23/2022,03/23/2022,
"1 filter applied","应用了 1 个过滤器",
2022-03-23,2022-03-23,
"23 Mar, 2022","23 03, 2022",
23-03-2022,23-03-2022,
23.03.2022,23.03.2022,
23/03/2022,23/03/2022,
**********,电话号码,
"A submittable entry is deleted only if it is in the cancelled state.",只有当可提交条目处于取消状态时，才会删除该条目,
Account,科目,
"Account ${0} does not exist.","账户 ${0} 不存在",
"Account Entries",帐户条目,
"Account Name",帐户名称,
"Account Type",帐户类型,
Accounting,会计,
"Accounting Entries",会计分录,
"Accounting Ledger Entry",会计分类帐输入,
"Accounting Settings",会计设置,
Accounts,账户,
"Accounts Payable",应付账款,
"Accounts Receivable",应收账款,
"Accumulated Depreciation",累计折旧,
Action,活动,
Active,有效,
"Add Account",添加帐户,
"Add Customers",添加客户,
"Add Group",添加组,
"Add Items",添加商品,
"Add Row",添加行,
"Add Suppliers",添加供应商,
"Add Taxes",加税,
"Add a few customers to create your first sales invoice",添加几个客户创建您的第一张销售订单,
"Add a few suppliers to create your first purchase invoice",添加一些供应商以创建您的第一张采购订单,
"Add a filter",添加过滤器,
"Add a remark",添加备注,
"Add attachment",添加附件,
"Add invoice terms",添加发票条款,
"Add products or services that you buy from your suppliers",添加您从供应商处购买的商品或服务,
"Add products or services that you sell to your customers",添加您向客户销售的商品或服务,
"Add transfer terms",添加转账条款,
"Add'l Discounts",,
"Additional ${0} Serial Numbers required for ${1} quantity of ${2}.","数量为 ${2} 的 ${1} 所需的额外$ ${0} 序列号",
"Additional quantity (${0}) required${1} to make outward transfer of item ${2} from ${3} on ${4}","额外数量 (${0}) 需要 ${1} 才能在 ${4} 从 ${3} 向外转移商品 ${2}",
Address,地址,
"Address Display",地址显示,
"Address Line 1","地址第 1 行",
"Address Line 2","地址第 2 行",
"Address Name",地址名称,
"Administrative Expenses",行政开支,
All,全部,
Amount,总计,
"Amount Paid",付款金额,
"Amount: ${0} and writeoff: ${1} is less than the total amount allocated to references: ${2}.","金额：${0} 和核销：${1} 少于分配给参考的总金额：${2}。",
"Amount: ${0} is less than the total amount allocated to references: ${1}.","金额：${0} 小于分配给参考的总金额：${1}。",
Amounts,金额,
"An entry is cancelled only if it is in the submitted state.",只有当条目处于已提交状态时，才会取消该条目,
"An entry is submitted only if it is submittable and is in the saved state.",条目只有在可提交且处于保存状态时才提交,
"An error occurred.",发生错误。,
"Applicable anywhere in Frappe Books","适用于Frappe Books的任何地方",
"Applicable when Quick Search is open",打开快速搜索时适用,
"Applicable when Template Builder is open",打开模板生成器时适用,
"Applicable when a entry is open in the Form view or Quick Edit view",在“表单”视图或“快速编辑”视图中打开条目时适用,
"Applicable when the List View of an entry type is open",当条目类型的列表视图打开时适用,
"Application of Funds (Assets)",资产,
"Apply Discount After Tax",应用税后折扣,
"Apply and view changes made to the print template",应用并查看对打印模板所做的更改,
April,四月,
Arial,Arial,
"Ascending Order",升序,
Asset,资产,
"Attach Image",附加图像,
Attachment,关联,
August,八月,
"Auto Payments",自动支付,
"Auto Stock Transfer",自动库存转移,
Autocomplete,自动完成,
Back,,
"Back Reference",反向关联,
"Bad import data, could not read file.",导入数据错误，无法读取文件,
Balance,平衡,
"Balance Amount",,
"Balance Sheet",资产负债表,
Bank,银行,
"Bank Accounts",银行账户,
"Bank Entry",银行条目,
"Bank Name",银行名称,
"Bank Overdraft Account",银行透支账户,
Barcode,条形码,
"Base Grand Total",总计,
"Based On",基于,
Batch,批次,
"Batch not set for row ${0}.","未为第 ${0} 行设置批次",
"Batch set for row ${0}.","第 ${0} 行的批处理集",
"Bill Created",账单已创建,
Billing,账务,
Black,黑色,
Blue,蓝色,
Both,两者,
"Both From and To Location cannot be undefined",“起始位置”和“终止位置”都不能未定义,
Buildings,建筑物,
Cancel,取消,
"Cancel ${0}?","取消 ${0}?",
"Cancel or Delete an entry.",取消或删除条目,
Cancelled,取消,
"Cannot Commit Error",无法提交错误,
"Cannot Delete",无法删除,
"Cannot Delete Account",无法删除帐户,
"Cannot Export",无法导出,
"Cannot Import",无法导入,
"Cannot Open File",无法打开文件,
"Cannot cancel ${0} ${1} because of the following ${2}: ${3}","由于以下 ${2} ，无法取消 ${0} ${1} : ${3}",
"Cannot cancel ${0} because of the following ${1}: ${2}",,
"Cannot delete ${0} ""${1}"" because of linked entries.","由于存在链接条目，无法删除 ${0} ""${1}"" ",
"Cannot open file",无法打开文件,
"Cannot perform operation.",无法执行操作,
"Cannot read file",无法读取文件,
"Capital Equipments",资本设备,
"Capital Stock",资本存量,
Cash,现金,
"Cash Denominations",,
"Cash Entry",现金入账,
"Cash In Hand",库存现金,
Cashflow,现金流,
"Central Tax",中央税率,
"Change DB",更改数据库,
"Change File",更改文件,
"Change Ref Type",更改关联类型,
"Changes made to settings will be visible on reload.",对设置所做的更改将在重新加载时可见,
Chargeable,收费,
"Chart Of Accounts Reviewed",已审核的会计科目表,
"Chart of Accounts",会计科目表,
Check,检查,
Cheque,检查,
"City / Town",城市,
Clear,清除,
"Clearance Date",清仓日期,
Close,关闭,
"Close Frappe Books and try manually.","关闭Frappe Books并手动尝试",
"Close POS Shift",,
"Close Quick Search",关闭快速搜索,
Closing,正在关闭,
"Closing ${0} Amount can not be negative.",,
"Closing (Cr)","正在关闭 (贷方)",
"Closing (Dr)","正在关闭 (借方)",
"Closing Amount",,
"Closing Cash In Denominations",,
"Closing Date",,
Collapse,折叠,
Color,颜色,
"Commission on Sales",销售佣金,
Common,常用,
Company,公司,
"Company Logo",公司标志,
"Company Name",公司名称,
"Company Setup",公司设置,
Completed,已完成,
Condition,条件,
"Consolidate Columns",合并列,
Contacts,联系人,
Contains,包含,
"Continue submitting Sales Invoice?",继续提交销售订单?,
"Contra Entry",反对进入,
"Conversion Error",转换错误,
"Conversion Factor",转换系数,
"Cost Of Goods Sold Acc.",销货成本科目,
"Cost of Goods Sold",商品销售成本,
"Could not connect to database file ${0}, please select the file manually","无法连接到数据库文件 ${0}，请手动选择该文件",
Count,,
"Counter Cash Account",,
Country,国家,
"Country Code",国家代码,
"Country code used to initialize regional settings.",用于初始化区域设置的国家/地区代码。,
Courier,快递,
Cr.,贷方,
"Cr. ${0}","贷方 ${0}",
Create,创建,
"Create Demo",创建演示,
"Create Purchase",创建采购,
"Create Purchase Invoice",创建采购订单,
"Create Sale",创建销售,
"Create Sales Invoice",创建销售订单,
"Create a demo company to try out Frappe Books","创建一个演示公司来试用Frappe Books",
"Create a new company and store it on your computer",创建一个新公司并将其存储在您的计算机上,
"Create a new company or select an existing one from your computer",创建新公司或从计算机中选择现有公司,
"Create a new entry of the same type as the List View",创建与列表视图类型相同的新条目,
"Create new ${0} entry?","是否创建新的 ${0} 条目？",
"Create your first purchase invoice from the created supplier",从创建的供应商创建您的第一张采购订单,
"Create your first sales invoice for the created customer",为创建的客户创建第一张销售订单,
Created,已创建,
"Created By",创建者,
"Creating Items and Parties",创建物品和客户,
"Creating Journal Entries",创建日记帐,
"Creating Purchase Invoices",创建采购订单,
Credit,贷方,
"Credit Card Entry",信用卡入口,
"Credit Note",信用票据,
Creditors,债权人,
Currency,货币,
"Currency Name",货币名称,
Current,当前,
"Current Assets",当前资产,
"Current Liabilities",流动负债,
"Custom Field",自定义字段,
"Custom Fields",自定义字段,
"Custom Form",自定义表单,
"Custom Hex",自定义十六进制,
Customer,客户,
"Customer Created",客户创建,
"Customer Currency",客户货币,
Customers,客户,
Customizations,定制,
"Customize Form",自定义窗体,
"Customize your invoices by adding a logo and address details",通过添加徽标和地址详细信息自定义您的发票,
Dashboard,仪表板,
Data,数据,
"Database Error",数据库错误,
"Database file: ${0}",数据库文件：${0},
Date,日期,
"Date Format",日期格式,
"Date Time",日期和时间,
Day,天,
Debit,借方,
"Debit Note",借记单,
Debtors,债务人,
December,十二月,
"Decrease print template display scale",降低打印模板显示比例,
Default,默认,
"Default Account",默认帐户,
"Default Cash Denominations",,
"Default Location",默认仓库,
Defaults,默认,
Delete,删除,
"Delete ${0}?","删除 ${0}？",
"Delete Account",删除帐户,
"Delete Failed",删除失败,
"Delete Group",删除组,
Delivered,交付,
Denomination,,
Depreciation,折旧,
"Depreciation Entry",折旧条目,
Description,描述,
Details,详细信息,
"Difference Amount",,
"Direct Expenses",直接开支,
"Direct Income",直接收入,
"Directory for database file ${0} does not exist, please select the file manually","数据库文件 ${0} 的目录不存在，请手动选择该文件",
Disabled,有残疾的,
"Discount Account",折扣帐户,
"Discount Account is not set.",未设置折扣帐户,
"Discount Amount",折扣金额,
"Discount Amount (${0}) cannot be greated than Amount (${1}).","折扣金额 (${0}) 不能大于金额 (${1})",
"Discount Percent",折扣百分比,
"Discount Percent (${0}) cannot be greater than 100.","折扣百分比 (${0}) 不能大于100",
"Discounted Amount",贴现金额,
Discounts,折扣,
"Display Doc",显示单据,
"Display Logo in Invoice",在发票上显示标志,
"Display Precision",显示精度,
"Display Precision should have a value between 0 and 9.","显示精度应具有介于 0 和 9 之间的值。",
"Display Scale",显示比例,
"Dividends Paid",已支付股息,
"Doc ${0} ${1} not set","未设置单据 ${0} ${1} ",
Docs,文档,
Documentation,文档,
"Does Not Contain",不包含,
Done,结束,
Dr.,借方,
"Dr. ${0}","借方 ${0}",
Draft,草稿,
Duplicate,重复,
"Duplicate Entry",重复条目,
"Duplicate Template",复制模板,
"Duplicate columns found: ${0}","发现重复列: ${0}",
"Duties and Taxes",关税和税率,
"Dynamic Link",动态链接,
"Earnest Money",定金,
"Electronic Equipments",电子设备,
Email,电子邮件,
"Email Address",电子邮件地址,
Empty,空的,
"Empty file selected",已选择空文件,
"Enable Barcodes",启用条形码,
"Enable Batches",启用批次,
"Enable Discount Accounting",启用折扣会计,
"Enable Form Customization",启用表单自定义,
"Enable Inventory",启用库存,
"Enable Invoice Returns",,
"Enable Point of Sale",,
"Enable Price List",启用价格表,
"Enable Serial Number",启用序列号,
"Enable Stock Returns",启用库存退货,
"Enable UOM Conversion",启用计量单位转换,
Enabled,启用,
"Enabled For",为启用,
"Enter Country to load States",输入国家以加载状态,
"Enter State",输入状态,
"Enter barcode",输入条形码,
"Entertainment Expenses",业务招待费,
Entry,条目,
"Entry Currency",输入货币,
"Entry Label",入口标签,
"Entry No",输入编号,
"Entry No.",条目编号,
"Entry Type",输入类型,
"Entry has Grand Total ${0}. Please verify amounts.","条目的总计为 ${0}。请核实金额",
Equity,权益,
Error,错误,
"Exchange Gain/Loss",汇兑收益/亏损,
"Exchange Rate",汇率,
"Excise Entry",消费税条目,
"Existing Company",现有公司,
Expand,展开,
"Expected Amount",,
Expense,费用,
"Expense Account",费用账户,
Expenses,成本,
"Expenses Included In Valuation",计价费用,
"Expiry Date",截止日期,
Export,导出,
"Export Failed",导出失败,
"Export Format",导出格式,
"Export Successful",导出成功,
"Export Wizard",导出向导,
Failed,失败,
Fax,传真,
Features,特征,
February,二月,
Field,字段,
Fieldname,字段名称,
"Fieldname ${0} already exists for ${1}"," ${1} 的字段名 ${0} 已存在",
"Fieldname ${0} already used for Custom Field ${1}","字段名 ${0} 已用于自定义字段 ${1}",
Fieldtype,字段类型,
"File ${0} does not exist.","文件 ${0} 不存在",
"File selection failed",文件选择失败,
Fill,填充,
Filter,过滤器,
"Fiscal Year",财政年度,
"Fiscal Year End Date",财政年度结束日期,
"Fiscal Year Start Date",财政年度开始日期,
"Fix Failed",修复失败,
"Fixed Asset",固定资产,
"Fixed Assets",固定资产,
Float,浮动,
"Following cells have errors: ${0}.","以下单元格有错误: ${0}",
"Following links do not exist: ${absentLinks .map((l) =>","以下链接不存在: ${absentLinks .map((l) =>",
Font,字体,
"For Purchase",用于采购,
"For Sales",用于销售,
"Forbidden Error",禁止的错误,
"Form Section",表格部分,
"Form Tab",表单选项卡,
"Form Type",表单类型,
Fr,,
Fraction,分数,
"Fraction Units",分数单位,
"Frappe Books does not have access to the selected file: ${0}","Frappe Books无权访问所选文件: ${0}",
"Freight and Forwarding Charges",运费和转运费用,
From,从,
"From Account",从帐户,
"From Date",从日期,
"From Loc.",,
"From Year",从年份,
"Full Name",全名,
"Furnitures and Fixtures",家具和固定设备,
GST,,
"GSTIN No.","GSTIN 编号",
GSTR1,GSTR1,
GSTR2,GSTR2,
"Gain/Loss on Asset Disposal",资产处置损益,
General,常规,
"General Ledger",总帐,
"Get Started",开始使用,
Global,全局,
"Go back to the previous page",返回上一页,
Gram,克,
"Grand Total",总计,
"Greater Than",大于,
Green,绿色,
"Group By",按分组,
HSN/SAC,HSN/SAC,
"HSN/SAC Code","HSN/SAC 代码",
"Half Yearly",每半年,
"Half Years",半年后,
"Has Batch",具有批次,
"Has Serial Number",具有序列号,
"Height (in cm)",高度(cm),
Help,帮助,
"Hex Value",十六进制值,
"Hidden values will be visible on Print on.",隐藏值将在打印时显示,
"Hide Get Started",隐藏入门,
"Hide Group Amounts",隐藏组金额,
"Hide Month/Year",隐藏月/年,
"Hides the Get Started section from the sidebar. Change will be visible on restart or refreshing the app.","从边栏中隐藏“入门”部分。 更改将在重新启动或刷新应用程序时可见。",
Hour,小时,
INR,INR,
Image,图像,
"Import Complete",导入完成,
"Import Data",导入数据,
"Import Data.",导入数据,
"Import Type",导入类型,
"Import Wizard",导入向导,
"Importer not set, reload tool",导入器未设置，重新加载工具,
Inactive,不活跃的,
"Include Cancelled",包括已取消,
Income,损益,
"Income Account",收入账户,
"Increase print template display scale",增加打印模板显示比例,
Indigo,靛蓝,
"Indirect Expenses",间接开支,
"Indirect Income",间接收入,
Inflow,流入,
"Instance Id","实例 ID",
"Insufficient Quantity",数量不足,
"Insufficient Quantity.",数量不足,
"Insufficient Quantity. Item ${0} has only ${1} quantities available. you selected ${2}",,
Int,,
"Intergrated Tax",综合所得税,
"Internal Precision",内部精度,
"Invalid Key Error",无效密钥错误,
"Invalid Quantity for Item ${0}",,
"Invalid barcode value ${0}.","无效条形码值 ${0}",
"Invalid value ${0} for ${1}","${1} 的值 ${0} 无效",
"Invalid value found for ${0}","为 ${0} 找到无效值",
Inventory,库存,
"Inventory Settings",库存设置,
Investments,投资,
Invoice,发票,
"Invoice Created",已创建发票,
"Invoice Date",发票日期,
"Invoice Item",发票商品,
"Invoice No",发票号码,
"Invoice No.",采购订单编号,
"Invoice Value",发票面额,
Invoices,发票,
Is,是,
"Is Custom",是否定制,
"Is Empty",是空的,
"Is Group",是组,
"Is Landscape",是否景观,
"Is Not",不是,
"Is Not Empty",不为空,
"Is POS Shift Open",,
"Is Price List Enabled",是否启用价目表,
"Is Required",是否需要,
"Is Whole",完整,
Item,商品,
"Item ${0} has Zero Quantity",,
"Item ${0} is a batched item","商品 ${0} 是批次商品",
"Item ${0} is not a batched item","商品 ${0} 不是批次商品",
"Item ${0} not in Stock",,
"Item Description",商品描述,
"Item Discounts",,
"Item Name",商品名称,
"Item Prices",商品价格,
"Item with From location not found",未找到带有发件人仓库的商品,
"Item with To location not found",未找到含有收件人仓库的商品,
"Item with barcode ${0} not found.","未找到带有条形码 ${0} 的商品",
Items,商品,
January,一月,
"John Doe",全名,
"Journal Entries",日记帐,
"Journal Entry",日记帐,
"Journal Entry Account",日记帐帐户,
"Journal Entry Number Series",日记账编号,
"Journal Entry Print Template",日记帐打印模板,
July,七月,
June,六月,
"Key Hints",关键提示,
Kg,千克,
Label,标签,
Language,语言,
"Left Index",左索引,
"Legal Expenses",法律开支,
"Less Filters",减少过滤器,
"Less Than",少于,
Liability,负债,
Limit,限制,
Link,链接,
"Link Validation Error",链接验证错误,
"Linked Entries",链接条目,
List,列表,
"List View",列表视图,
"Load an existing company from your computer",从电脑加载现有公司,
"Loading Report...",正在加载报告...,
"Loading instance...",正在加载实例...,
Loading...,正在加载...,
"Loans (Liabilities)",贷款（负债）,
"Loans and Advances (Assets)",贷款和垫款（资产）,
Locale,仓库设置,
Location,仓库,
"Location Name",仓库名,
Logo,徽标,
"Make Entry",进行条目,
"Make Payment On Submit",提交时付款,
"Make Purchase Receipt On Submit",提交购买收据,
"Make Shipment On Submit",提交后发货,
"Mandatory Error",强制性错误,
Manufacture,制造,
"Manufacture Date",生产日期,
"Mar 23, 2022","03 23"," 2022"
March,三月,
"Mark ${0} as submitted?","将 ${0} 标记为已提交？",
"Marketing Expenses",营销费用,
"Material Issue",材料问题,
"Material Receipt",材料收据,
"Material Transfer",材料转运,
May,五月,
Meter,米,
Misc,杂项,
Miscellaneous,其它,
"Miscellaneous Expenses",杂项费用,
Mo,,
Modified,已修改,
"Modified By",修改者,
Monthly,每月一次,
Months,月份,
More,更多,
"More Filters",更多过滤器,
"More shortcuts will be added soon.",不久将添加更多快捷方式,
"Movement Type",动作类型,
Name,名称,
Navigate,导航,
"Need ${0} Serial Numbers for Item ${1}. You have provided ${2}",,
"Net Total",总金额,
"New ${0}","新建 ${0}",
"New ${0} ${1}","新 ${0} ${1}",
"New Account",新建账户,
"New Company",新公司,
"New Entry",新条目,
"New Template",新模板,
No,否,
"No Display Entries Found",未找到显示条目,
"No Print Templates not found for entry type ${0}","未找到条目类型 ${0} 的打印模板",
"No Value",无数值,
"No Values to be Displayed",无值显示,
"No entries found",没有发现记录,
"No entries were imported.",没有输入任何条目,
"No expenses in this period",这期间没有任何费用,
"No filters selected",没有选择过滤器,
"No linked entries found",未找到链接条目,
"No results found",没有找到结果,
"No rows added. Select a file or add rows.",未添加行。选择文件或添加行,
"No transactions yet",没有交易,
"Non Active Serial Number ${0} cannot be used as Manufacture raw material","非活动序列号 ${0} 不能用作制造原材料",
"Non Active Serial Number ${0} cannot be used for Material Issue","非活动序列号 ${0} 不能用于材料发放",
"Non Active Serial Number ${0} cannot be used for Material Transfer","非活动序列号 ${0} 不能用于材料转运",
"Non Inactive Serial Number ${0} cannot be used for Material Receipt","非不活动序列号 ${0} 不能用于材料收据",
None,无,
"Not Found",未找到,
"Not Saved",未保存,
"Not Submitted",未提交,
"Not Transferred",未转让,
Notes,备注,
November,十一月,
"Number Display",数字显示,
"Number Series",系列号,
"Number of ${0}","${0} 的数量",
"Number of Rows",行数,
October,十月,
"Office Equipments",办公设备,
"Office Maintenance Expenses",办公室维修费用,
"Office Rent",办公室租金,
Okay,好的,
"Onboarding Complete",入职完成,
"Only From or To can be set for Manufacture","只能为 ""制造地 ""设置 ""从 ""或 ""到""。",
"Open Count",公开计数,
"Open Documentation",公开文档,
"Open Folder",打开文件夹,
"Open Print View",打开打印视图,
"Open Print View if Print is available.",如果可以打印，则打开打印视图,
"Open Quick Search",打开快速搜索,
"Open Report Print View",打开报告打印视图,
"Open the Export Wizard modal",打开导出向导模式,
"Opening (Cr)","正在打开 (贷方)",
"Opening (Dr)","正在打开 (借方)",
"Opening Amount",,
"Opening Balance Equity",期初余额权益,
"Opening Balances",期初余额,
"Opening Cash Amount can not be negative.",,
"Opening Cash In Denominations",,
"Opening Date",,
"Opening Entry",打开条目,
Options,选项,
Orange,橙色,
Organisation,机构,
Outflow,流出,
Outstanding,在途,
"Outstanding Amount",待付金额,
POS,,
"POS Counter Cash Account is not set. Please set it on POS Settings",,
"POS Customer",,
"POS Inventory is not set. Please set it on POS Settings",,
"POS Settings",,
"POS Shift Amount",,
"POS Write Off Account is not set. Please set it on POS Settings",,
"Pad Zeros",填充零,
Page,页面,
Paid,已支付,
"Paid ${0}","已支付 ${0}",
"Paid Change",,
Parent,父母,
"Parent Account",父帐户,
Party,客户,
"Patch Run",修补程序运行,
Pay,支付,
Payable,应付,
Payment,支付,
"Payment ${0} is Saved",,
"Payment For",付款方式,
"Payment Method",付款方式,
"Payment No",付款号,
"Payment Number Series",付款单编号,
"Payment Print Template",付款打印模板,
"Payment Reference",付款凭据,
"Payment Type",付款方式,
"Payment amount cannot be ${0}.","付款金额不能为 ${0}。",
"Payment amount cannot be less than zero.",付款金额不能小于零。,
"Payment amount cannot exceed ${0}.","付款金额不能超过 ${0}。",
"Payment amount: ${0} should be greater than 0.","付款金额：${0} 应大于 0。",
"Payment amount: ${0} should be less than Outstanding amount: ${1}.","付款金额：${0} 应小于待付金额：${1}。",
"Payment of ${0} will be made from account ""${1}"" to account ""${2}"" on Submit.","将在提交时从账户""${1}""向账户""${2}""支付${0}。",
Payments,付款,
"Payroll Payable",应付工资,
"Pending Qty. ${0}","待收数量 ${0}",
"Pending qty. ${0}","待收数量 ${0}",
Periodicity,周期性,
Phone,电话,
"Pick Columns",选择列,
"Pick Import Columns",选择导入栏,
Pink,粉红色,
Place,场所,
"Place of supply",供应地,
"Plants and Machineries",设备和机械,
"Please Wait",请稍等,
"Please check Key Hints for valid key names",请查看密钥提示，了解有效的密钥名称,
"Please create a ${0} entry to view Template Preview.","请创建 ${0} 条目以查看模板预览",
"Please fill all values.",请填写所有数值,
"Please restart and try again.",请重新启动并再试一次,
"Please select a Print Template",请选择打印模板,
"Please select a valid reference type.",请选择有效的关联类型。,
"Please set GSTIN in General Settings.","请在常规设置中设置 GSTIN",
"Please set Round Off Account in the Settings.",请在设置中设置四舍五入账户,
"Please set a Display Doc",请设置显示文档,
"Point of Sale",,
"Postal Code",邮政编码,
"Postal Expenses",邮政费用,
"Posting Date",发布日期,
Prefix,前缀,
"Price List",价格表,
"Price List Item",价格表商品,
"Prime Bank",主要银行,
Print,打印,
"Print ${0}","打印 ${0}",
"Print Settings",打印设置,
"Print Setup",打印设置,
"Print Template",打印模板,
"Print Template Name not set",未设置打印模板名称,
"Print Template is empty",打印模板为空,
"Print Templates",打印模板,
"Print View",打印查看,
"Print and Stationery",打印和文具,
Product,商品,
"Profit And Loss",收益与损失,
"Profit and Loss",收益与损失,
Purchase,采购,
"Purchase Acc.",购买协议,
"Purchase Invoice",采购订单,
"Purchase Invoice Item",采购订单商品,
"Purchase Invoice Number Series",采购订单编号,
"Purchase Invoice Print Template",采购订单打印模板,
"Purchase Invoice Terms",采购订单条款,
"Purchase Invoices",采购订单,
"Purchase Item",采购商品,
"Purchase Item Created",已创建采购商品,
"Purchase Items",采购商品,
"Purchase Payment",付款单,
"Purchase Payment Account",付款账户,
"Purchase Payments",付款单,
"Purchase Receipt",入库单,
"Purchase Receipt Item",入库商品,
"Purchase Receipt Location",入库仓,
"Purchase Receipt Number Series",入库单编号,
"Purchase Receipt Print Template",入库单打印模板,
"Purchase Receipt Terms",采购条款,
Purchases,采购,
Purple,紫色,
Purpose,目的,
"Qty in Batch",,
"Qty. ${0}","数量 ${0}",
"Qty. in Transfer Unit",传送装置中的数量,
Quantity,数量,
"Quantity (${0}) has to be greater than zero","数量 (${0}) 必须大于零",
"Quantity needs to be set",需要设置数量,
Quarterly,每季,
Quarters,四分之一,
"Quick Search",快速搜索,
"Quick edit error: ${0} entry has no name.","快速编辑错误：${0} 条目没有名称",
Quote,,
"Quote Reference",,
Rate,价格,
"Rate (${0}) cannot be less zero.","价格 (${0}) 不能小于零。",
"Rate (${0}) has to be greater than zero","价格 (${0}) 必须大于零",
"Rate can't be negative.",价格不能为负。,
"Rate needs to be set",需要设定价格,
"Raw Value: ${0}","原始值： ${0}",
Receivable,应收账款,
Receive,收到,
Red,红色,
"Ref Name",关联名称,
"Ref Type",关联类型,
"Ref. / Cheque No.",关联/检查号码,
"Ref. Date",关联日期,
"Ref. Name",关联单号,
"Ref. Type",关联类型,
Reference,关联,
"Reference Date",关联日期,
"Reference Number",关联单号,
"Reference Type",关联类型,
References,关联资料,
"Reload Frappe Books?","重新加载 Frappe Books?",
Report,报告,
"Report Error",报告错误,
"Report Issue",报告问题,
"Report will use more than one page if required.",如果需要，报告将使用多于一页的篇幅,
Reports,报告,
"Required fields not selected: ${0}","未选择必填字段： ${0}",
"Retained Earnings",保留收益,
Return,返回,
"Return Against",拒收退货,
"Return Issued",已签发退货单,
"Reverse Chrg.",反向征收机制,
Reverted,已恢复,
Reverts,恢复,
"Review Accounts",审核帐户,
"Review your chart of accounts, add any account or tax heads as needed",查看您的科目表，根据需要添加任何科目或税目,
"Right Index",右索引,
Role,角色,
"Root Type",根类型,
"Round Off",四舍五入,
"Round Off Account",四舍五入帐户,
"Round Off Account Not Found",未找到四舍五入帐户,
"Rounded Off",四舍五入,
"Row ${0}","行 ${0}",
Sa,,
Salary,薪水,
Sales,销售,
"Sales Acc.",销售记录,
"Sales Expenses",销售费用,
"Sales Invoice",销售订单,
"Sales Invoice ${0} is Submitted",,
"Sales Invoice Item",销售订单商品,
"Sales Invoice Number Series",销售订单编号,
"Sales Invoice Print Template",销售订单打印模板,
"Sales Invoice Terms",销售订单条款,
"Sales Invoices",销售订单,
"Sales Item",销售商品,
"Sales Item Created",已创建销售商品,
"Sales Items",销售商品,
"Sales Payment",收款单,
"Sales Payment Account",收款账户,
"Sales Payments",收款单,
"Sales Quote",,
"Sales Quote Item",,
"Sales Quote Number Series",,
"Sales Quote Print Template",,
"Sales Quotes",,
"Sales and Purchase",销售和采购,
Save,保存,
"Save ${0}?","保存 ${0}?",
"Save Customizations",保存自定义,
"Save Template",保存模板,
"Save Template File",保存模板文件,
"Save as PDF","另存为 PDF",
"Save as PDF Successful","另存为 PDF 成功",
"Save changes made to ${0}?","保存对 ${0} 所做的更改？",
"Save or Submit an entry.",保存或提交参赛作品,
Saved,已保存,
"Search an Item",,
"Secured Loans",担保贷款,
"Securities and Deposits",证券和存款,
Select,选择,
"Select CoA","选择 CoA",
"Select Color",选择颜色,
"Select Country",选择国家,
"Select File",选择文件,
"Select Image",选择图片,
"Select Template File",选择模板文件,
"Select a Display Doc to view the Template",选择显示文档以查看模板,
"Select a Template type",选择模板类型,
"Select a form type to customize",选择要定制的表格类型,
"Select a pre-defined page size, or set a custom page size for your Print Template.",选择预定义的页面大小，或为打印模板设置自定义页面大小,
"Select column",选择栏,
"Select file",选择文件,
"Select folder",选择文件夹,
"Select the template type.",,
Selected,选定,
September,九月,
"Serial Number",编号,
"Serial Number ${0} does not belong to the item ${1}.","序列号 ${0} 不属于商品 ${1}",
"Serial Number ${0} does not exist.","序列号 ${0} 不存在",
"Serial Number ${0} is not Active.","序列号 ${0} 未激活",
"Serial Number ${0} is not Inactive","序列号 ${0} 未激活",
"Serial Number ${0} status is not Active.",,
"Serial Number Description","序列号 说明",
"Serial Number is enabled for Item ${0}","商品 ${0} 的序列号已启用",
"Serial Number is not enabled for Item ${0}","商品 ${0} 的序列号未启用",
"Serial Number not set for row ${0}.","未为 ${0} 行设置序列号",
"Serial Number set for row ${0}.","为 ${0} 行设置序列号",
Service,服务,
"Set Discount Amount",设置折扣金额,
"Set Period",设定周期,
"Set Print Size",设置打印尺寸,
"Set Template Type",,
"Set Up",设置,
"Set Up Your Workspace",设置您的工作区,
"Set a Template value to see the Print Template",设置“模板”值以查看“打印模板”,
"Set an Import Type",设置导入类型,
"Set the display language.",设置显示语言。,
"Set the local code. This is used for number formatting.","设置本地代码。 这用于数字格式。",
"Set up your company information, email, country and fiscal year",设置您的公司信息、电子邮件、国家和财政年度,
"Set up your opening balances before performing any accounting entries",在执行任何会计分录之前设置您的期初余额,
"Set up your organization",设置您的组织,
"Set up your tax templates for your sales or purchase transactions",为您的销售或采购交易设置税务模板,
"Sets how many digits are shown after the decimal point.",设置小数点后显示的位数。,
"Sets the app-wide date display format.",设置应用范围的日期显示格式。,
"Sets the internal precision used for monetary calculations. Above 6 should be sufficient for most currencies.","设置用于货币计算的内部精度。 对于大多数货币来说，6 以上应该足够了。",
"Setting Up Instance",设置实例,
Settings,设置,
Setup,设置,
"Setup Complete",设置完成,
"Setup Wizard",安装向导,
"Setup system defaults like date format and display precision",设置系统默认值，如日期格式和显示精度,
Shipment,发货单,
"Shipment ${0} is Submitted",,
"Shipment Item",发货商品,
"Shipment Location",发货仓,
"Shipment Number Series",发货单编号,
"Shipment Print Template",发货单打印模板,
"Shipment Terms",发货条款,
Shortcuts,快捷方式,
"Should entries be submitted after syncing?",是否在同步后提交？,
"Show HSN","显示 HSN",
"Show Me",给我看看,
"Show Month/Year",显示月份/年份,
"Single Value",单值,
"Skip Child Tables",跳过表,
"Skip Transactions",跳过业务,
"Smallest Currency Fraction Value",最小货币分数值,
Softwares,软件,
"Something has gone terribly wrong. Please check the console and raise an issue.","发生了严重的错误。 请检查控制台并提出问题。",
"Source of Funds (Liabilities)",负债,
"Standard Chart of Accounts",标准会计科目表,
Start,开始,
"Start From Row Index",从行索引开始,
State,状态,
"State Tax",国家税率,
Status,状态,
Stock,库存,
"Stock Adjustment",库存调整,
"Stock Assets",库存资产,
"Stock Balance",库存余额,
"Stock Entries",库存记录,
"Stock Expenses",库存费用,
"Stock In Hand",现有库存,
"Stock In Hand Acc.",库存在手账户,
"Stock Ledger",库存分类账,
"Stock Ledger Entry",库存分类账输入,
"Stock Liabilities",库存负债,
"Stock Movement",调拨单,
"Stock Movement Item",库存调拨,
"Stock Movement No.",库存调拨,
"Stock Movement Number Series",调拨单编号,
"Stock Movement Print Template",调拨单打印模板,
"Stock Movements",调拨单,
"Stock Not Received",在途库存,
"Stock Not Shipped",未发货库存,
"Stock Not Transferred",未转移库存,
"Stock Received But Not Billed",库存已收到但未开票,
"Stock Received But Not Billed Acc.",已收到但未开具账单的库存,
"Stock Transfer Item",库存转让商品,
"Stock Unit",库存单位,
StockTransfer,库存转让,
Stores,总仓库,
Su,,
Submit,提交,
"Submit ${0}?","提交 ${0}",
"Submit & Print",,
"Submit entries?",提交条目?,
Submitted,已提交,
Success,成功,
Supplier,供应商,
"Supplier Created",供应商创建,
Suppliers,供应商,
Symbol,符号,
System,系统,
"System Settings",系统设置,
"System Setup",系统设置,
Table,表格,
Target,目标,
Tax,税率,
"Tax Account",税务账户,
"Tax Amount",税额,
"Tax Assets",税率资产,
"Tax Detail",税务详细信息,
"Tax ID",税号,
"Tax Invoice Account",,
"Tax Payment Account",,
"Tax Rate",税率,
"Tax Summary",税务摘要,
"Tax Template",税务模板,
"Tax Templates",税务模板,
"Tax and Totals",税金和总计,
"Taxable Value",应税值,
"Taxed Amount",征税金额,
Taxes,税率,
"Taxes and Charges",,
Teal,蓝绿色,
"Telephone Expenses",电话费,
Template,模板,
"Template Builder",模板生成器,
"Template Compilation Error",模板编译错误,
"Template Name",模板名称,
"Template Type",模板类型,
"Template file saved",保存模板文件,
Temporary,暂时,
"Temporary Accounts",临时账户,
"Temporary Opening",临时开放,
Terms,条款,
Text,文本,
Th,,
"The following characters cannot be used ${0} in a Number Series name.","数字系列名称中不能使用以下字符 ${0}",
"The following items have insufficient quantity for Shipment: ${0}","以下物品数量不足，无法发货： ${0}",
"This Month",本月,
"This Quarter",本季度,
"This Year",今年,
"This action is permanent",此操作是永久性的,
"This action is permanent and will cancel the following payment: ${0}",此操作是永久性的，并将取消以下付款：${0},
"This action is permanent and will cancel the following payments: ${0}",此操作是永久性的，并将取消以下付款：${0},
"This action is permanent and will delete associated ledger entries.",此操作是永久性的，并将删除相关的分类帐条目。,
"This action is permanent.",此操作是永久性的。,
"Times New Roman","Times New Roman",
To,到,
"To Account",到账,
"To Account and From Account can't be the same: ${0}",到账户和从帐户不能相同：${0},
"To Date",到日期,
"To Loc.",入库仓,
"To Year",到年,
"Toggle Edit Mode",切换编辑模式,
"Toggle Key Hints",切换按键提示,
"Toggle Linked Entries widget, not available in Quick Edit view.",切换链接条目部件，在快速编辑视图中不可用,
"Toggle between form and full width",在表格和全宽之间切换,
"Toggle sidebar",切换侧边栏,
"Toggle the Create filter",切换创建过滤器,
"Toggle the Docs filter",切换文档过滤器,
"Toggle the List filter",切换列表过滤器,
"Toggle the Page filter",切换页面过滤器,
"Toggle the Report filter",切换报告筛选器,
"Top Expenses",费用明细,
Total,共计,
"Total Amount",总金额,
"Total Asset (Debit)",总资产（借方）,
"Total Debit: ${0} must be equal to Total Credit: ${1}","总借方：${0} 必须等于总贷方：${1}",
"Total Discount",折扣总额,
"Total Equity (Credit)",总权益（信用）,
"Total Expense (Debit)",总费用（借方）,
"Total Income (Credit)",总收入（贷方）,
"Total Liability (Credit)",总负债（贷方）,
"Total Profit",总利润,
"Total Quantity",,
"Total Spending",总支出,
"Track Inventory",库存跟踪,
Transfer,转移,
"Transfer No",转账,
"Transfer Type",转移类型,
"Transfer Unit",转让股,
"Transfer Unit ${0} is not applicable for Item ${1}","转账单位 ${0} 不适用于商品 ${1}",
"Transfer will cause future entries to have negative stock.",转账将导致未来分录中的库存为负值,
"Travel Expenses",旅行开支,
"Trial Balance",试算平衡表,
Tu,,
Type,类型,
"Type to search...",输入搜索...,
UOM,单位,
"UOM Conversion Item",物料单位换算,
"UOM Conversions",单位换算,
Unit,单元,
"Unit Type",单位类型,
Unpaid,未支付,
"Unpaid ${0}","未支付 ${0}",
"Unsecured Loans",无抵押贷款,
"Until Date",截止日期,
"Use Full Width",使用全宽,
"Use List Filters",使用列表过滤器,
"User Remark",用户备注,
"Utility Expenses",水电费,
"Validation Error",验证错误,
Value,值,
"Value missing for ${0}","${0} 的值缺失",
"Value: ${0}","值: ${0}",
Version,版本,
View,查看,
"View Accounting Entries",查看会计分录,
"View Paid Invoices",查看已支付发票,
"View Purchases",查看采购,
"View Sales",查看销售,
"View Stock Entries",查看库存条目,
"View Unpaid Invoices",查看未支付发票,
"View linked entries",查看链接条目,
We,我们,
"Welcome to Frappe Books","欢迎使用 Frappe Books",
"Width (in cm)","宽度 (cm)",
"Write Off",核销,
"Write Off Account",核销账户,
"Write Off Account ${0} does not exist. Please set Write Off Account in General Settings","核销帐户 ${0} 不存在。请在常规设置中设置核销科目",
"Write Off Account not set. Please set Write Off Account in General Settings","未设置核销帐户。 请在通用设置中设置核销账户",
"Write Off Entry",核销条目,
"Year to Date",年初至今,
Yearly,每年,
Years,年,
Yellow,黄色,
Yes,是,
"check values and click on",检查值并单击,
"in Batch ${0}","在批次 ${0} 中",
<EMAIL>,电子邮件,
"to apply changes",应用更改,