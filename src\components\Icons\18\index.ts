import Check from './check.vue';
import CommonEntries from './common-entries.vue';
import Customer from './customer.vue';
import Dashboard from './dashboard.vue';
import Fb from './fb.vue';
import General from './general.vue';
import Gst from './gst.vue';
import Inventory from './inventory.vue';
import Invoice from './invoice.vue';
import Item from './item.vue';
import Mail from './mail.vue';
import POS from './pos.vue';
import OpeningAc from './opening-ac.vue';
import Percentage from './percentage.vue';
import Property from './property.vue';
import PurchaseInvoice from './purchase-invoice.vue';
import Purchase from './purchase.vue';
import Reports from './reports.vue';
import ReviewAc from './review-ac.vue';
import SalesInvoice from './sales-invoice.vue';
import Sales from './sales.vue';
import Settings from './settings.vue';
import Start from './start.vue';
import Supplier from './supplier.vue';
import System from './system.vue';

// prettier-ignore
export default {
  'check': Check,
  'common-entries': CommonEntries,
  'customer': Customer,
  'dashboard': Dashboard,
  'fb': Fb,
  'general': General,
  'gst': Gst,
  'inventory': Inventory,
  'invoice': Invoice,
  'item': Item,
  'mail': Mail,
  'pos': POS,
  'opening-ac': OpeningAc,
  'percentage': Percentage,
  'property': Property,
  'purchase-invoice': PurchaseInvoice,
  'purchase': Purchase,
  'reports': Reports,
  'review-ac': ReviewAc,
  'sales-invoice': SalesInvoice,
  'sales': Sales,
  'settings': Settings,
  'start': Start,
  'supplier': Supplier,
  'system': System,
}
