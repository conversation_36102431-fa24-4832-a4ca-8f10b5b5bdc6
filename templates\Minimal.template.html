<main class="h-full" :style="{ 'font-family': print.font }">
  <!-- Invoice Header -->
  <header class="flex items-center justify-between w-full border-b px-12 py-10">
    <!-- Left Section -->
    <section class="flex items-center">
      <img
        v-if="print.displayLogo"
        class="h-12 max-w-32 object-contain mr-4"
        :src="print.logo"
      />

      <div>
        <p class="font-semibold text-xl" :style="{ color: print.color }">
          {{ print.companyName }}
        </p>

        <div class="flex gap-2">
          <p>{{ doc.date }}</p>
          <p>{{ doc?.time }}</p>
        </div>
      </div>
    </section>

    <!-- Right Section -->
    <section class="text-right">
      <p class="font-semibold text-xl" :style="{ color: print.color }">
        {{ doc.entryLabel }}
      </p>
      <p>{{ doc.name }}</p>
    </section>
  </header>

  <!-- Party && Company Details -->
  <section class="flex px-12 py-10 border-b">
    <!-- Party Details -->
    <section class="w-1/2">
      <h3 class="uppercase text-sm font-semibold tracking-widest text-gray-800">
        {{ doc.entryType === 'SalesInvoice' ? 'To' : 'From' }}
      </h3>
      <p class="mt-4 text-black leading-relaxed text-lg">{{ doc.party }}</p>
      <p
        v-if="doc.links.party.address"
        class="mt-2 text-black leading-relaxed text-lg"
      >
        {{ doc.links.party.links.address.addressDisplay ?? '' }}
      </p>
      <p
        v-if="doc.links.party.gstin"
        class="mt-2 text-black leading-relaxed text-lg"
      >
        GSTIN: {{ doc.links.party.gstin }}
      </p>
    </section>

    <!-- Company Details -->
    <section class="w-1/2">
      <h3
        class="
          uppercase
          text-sm
          font-semibold
          tracking-widest
          text-gray-800
          ml-8
        "
      >
        {{ doc.entryType === 'SalesInvoice' ? 'From' : 'To' }}
      </h3>
      <p class="mt-4 ml-8 text-black leading-relaxed text-lg">
        {{ print.companyName }}
      </p>
      <p
        v-if="print.address"
        class="mt-2 ml-8 text-black leading-relaxed text-lg"
      >
        {{ print.links.address.addressDisplay }}
      </p>
      <p
        v-if="print.gstin"
        class="mt-2 ml-8 text-black leading-relaxed text-lg"
      >
        GSTIN: {{ print.gstin }}
      </p>
    </section>
  </section>

  <!-- Items Table -->
  <section class="px-12 py-10 border-b">
    <!-- Heading Row -->
    <section
      class="
        mb-4
        flex
        uppercase
        text-sm
        tracking-widest
        font-semibold
        text-gray-800
      "
    >
      <div class="w-4/12 text-left">{{ t`Item` }}</div>
      <div class="w-2/12 text-right" v-if="doc.showHSN">{{ t`HSN/SAC` }}</div>
      <div class="w-2/12 text-right">{{ t`Quantity` }}</div>
      <div class="w-3/12 text-right">{{ t`Rate` }}</div>
      <div class="w-3/12 text-right">{{ t`Amount`}}</div>
    </section>

    <!-- Body Rows -->
    <section class="flex py-1 text-lg" v-for="row in doc.items" :key="row.name">
      <div class="w-4/12 text-left">{{ row.item }}</div>
      <div class="w-2/12 text-right" v-if="doc.showHSN">{{ row.hsnCode }}</div>
      <div class="w-2/12 text-right">{{ row.quantity }}</div>
      <div class="w-3/12 text-right">{{ row.rate }}</div>
      <div class="w-3/12 text-right">{{ row.amount }}</div>
    </section>
  </section>

  <!-- Invoice Footer -->
  <footer class="flex px-12 py-10">
    <!-- Invoice Terms -->
    <section class="w-1/2" v-if="doc.terms">
      <h3 class="uppercase text-sm tracking-widest font-semibold text-gray-800">
        {{ t`Notes` }}
      </h3>
      <p class="mt-4 text-lg whitespace-pre-line">{{ doc.terms }}</p>
    </section>

    <!-- Totaled Amounts -->
    <section class="w-1/2 text-lg ml-auto">
      <!-- Subtotal -->
      <div class="flex pl-2 justify-between py-1">
        <h3>{{ t`Subtotal` }}</h3>
        <p>{{ doc.netTotal }}</p>
      </div>

      <!-- Discount (if applied before tax) -->
      <div
        class="flex pl-2 justify-between py-1"
        v-if="doc.totalDiscount && !doc.discountAfterTax"
      >
        <h3>{{ t`Discount` }}</h3>
        <p>{{ doc.totalDiscount }}</p>
      </div>

      <!-- Tax Breakdown -->
      <div
        class="flex pl-2 justify-between py-1"
        v-for="tax in doc.taxes"
        :key="tax.name"
      >
        <h3>{{ tax.account }}</h3>
        <p>{{ tax.amount }}</p>
      </div>

      <!-- Discount (if applied after tax) -->
      <div
        class="flex pl-2 justify-between py-1"
        v-if="doc.totalDiscount && doc.discountAfterTax"
      >
        <h3>{{ t`Discount` }}</h3>
        <p>{{ doc.totalDiscount }}</p>
      </div>

      <!-- Grand Total -->
      <div
        class="flex pl-2 justify-between py-1 font-semibold"
        :style="{ color: print.color }"
      >
        <h3>{{ t`Grand Total` }}</h3>
        <p>{{ doc.grandTotal }}</p>
      </div>
    </section>
  </footer>
</main>
