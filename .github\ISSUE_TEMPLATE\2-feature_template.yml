name: '💡 Feature Request'
description: Create a new ticket for a new feature request
title: '💡 [Feature Request] - <title>'
labels: ['enhancement']

body:
  - type: textarea
    id: summary
    attributes:
      label: 'Summary'
      description: Provide a brief explanation of the feature
      placeholder: Describe in a few lines your feature request
    validations:
      required: true

  - type: textarea
    id: benefits
    attributes:
      label: 'What problem are you trying to solve?'
      description: Tell us what is the thing you are doing and why this feature would help you in that
      placeholder: Describe the problem or issue that the feature would solve
    validations:
      required: true

  - type: textarea
    id: basic_example
    attributes:
      label: 'Basic Example'
      description: Indicate here some basic examples of your feature.
      placeholder: A few specific words about your feature request.
    validations:
      required: true

  - type: textarea
    id: drawbacks
    attributes:
      label: 'Drawbacks'
      description: What are the drawbacks/impacts of your feature request ?
      placeholder: Identify the drawbacks and impacts while being neutral on your feature request
    validations:
      required: true

  - type: textarea
    id: reference_issues
    attributes:
      label: 'Reference Issues'
      description: Common issues
      placeholder: '#Issues IDs'
    validations:
      required: false
