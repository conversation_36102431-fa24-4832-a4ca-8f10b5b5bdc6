{"name": "POSSettings", "label": "POS Settings", "isSingle": true, "isChild": false, "fields": [{"fieldname": "inventory", "label": "Inventory", "fieldtype": "Link", "target": "Location", "create": true, "default": "Stores", "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "cashAccount", "label": "Counter Cash Account", "fieldtype": "Link", "target": "Account", "default": "Cash In Hand", "required": true, "create": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "writeOffAccount", "label": "Write Off Account", "fieldtype": "Link", "target": "Account", "create": true, "default": "Write Off", "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "posUI", "label": "<PERSON>s <PERSON>", "fieldtype": "Select", "options": [{"value": "Classic", "label": "Classic"}, {"value": "Modern", "label": "Modern"}], "default": "Classic", "required": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "isShiftOpen", "label": "Is POS Shift Open", "fieldtype": "Check", "default": false, "hidden": true}, {"fieldname": "defaultAccount", "label": "default Account", "fieldtype": "Link", "create": true, "required": true, "target": "Account", "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "weightEnabledBarcode", "label": "Weight Enabled Barcode", "fieldtype": "Check", "default": false, "section": "Barcode"}, {"fieldname": "checkDigits", "label": "Check Digits", "fieldtype": "Int", "default": 0, "section": "Barcode"}, {"fieldname": "itemCodeDigits", "label": "Item Code Digits", "fieldtype": "Int", "default": 0, "section": "Barcode"}, {"fieldname": "itemWeightDigits", "label": "item Weight Digits", "fieldtype": "Int", "default": 0, "section": "Barcode"}, {"fieldname": "itemVisibility", "label": "Item Visibility", "fieldtype": "Select", "options": [{"value": "Inventory Items", "label": "Inventory Items"}, {"value": "Non-Inventory Items", "label": "Non-Inventory Items"}], "default": "Inventory Items", "required": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "canChangeRate", "label": "Can Change Rate", "fieldtype": "Check", "default": false, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "hideUnavailableItems", "label": "Hide Unavailable Items", "fieldtype": "Check", "default": false, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "canEditDiscount", "label": "Can Edit Discount", "fieldtype": "Check", "default": false, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "ignorePricingRule", "label": "Ignore Pricing Rule", "fieldtype": "Check", "section": "<PERSON><PERSON><PERSON>", "default": false}]}