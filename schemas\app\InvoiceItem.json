{"name": "InvoiceItem", "label": "Invoice Item", "isAbstract": true, "isChild": true, "fields": [{"fieldname": "item", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "create": true, "required": true}, {"fieldname": "itemCode", "label": "ItemCode", "fieldtype": "Data", "hidden": true}, {"fieldname": "description", "label": "Description", "fieldtype": "Text"}, {"fieldname": "rate", "label": "Rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "required": true}, {"fieldname": "transferUnit", "label": "Transfer Unit", "fieldtype": "Link", "target": "UOM", "default": "Unit", "placeholder": "Unit"}, {"fieldname": "transferQuantity", "label": "Qty. in Transfer Unit", "fieldtype": "Float", "default": 1, "required": true}, {"fieldname": "unit", "label": "Stock Unit", "fieldtype": "Link", "target": "UOM", "default": "Unit", "placeholder": "Unit", "readOnly": true}, {"fieldname": "batch", "label": "<PERSON><PERSON>", "fieldtype": "Link", "create": true, "target": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>"}, {"fieldname": "quantity", "label": "Quantity", "fieldtype": "Float", "required": true, "default": 1}, {"fieldname": "unitConversionFactor", "label": "Conversion Factor", "fieldtype": "Float", "required": true, "default": 1}, {"fieldname": "account", "label": "Account", "fieldtype": "Link", "target": "Account", "required": true}, {"fieldname": "tax", "label": "Tax", "fieldtype": "Link", "create": true, "target": "Tax"}, {"fieldname": "amount", "label": "Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true}, {"fieldname": "setItemDiscountAmount", "label": "Set Discount Amount", "fieldtype": "Check", "default": false}, {"fieldname": "itemDiscountAmount", "label": "Discount Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": false}, {"fieldname": "itemDiscountPercent", "label": "Discount Percent", "fieldtype": "Float", "readOnly": false}, {"fieldname": "itemDiscountedTotal", "label": "Discounted Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": false, "computed": true}, {"fieldname": "itemTaxedTotal", "label": "Taxed Amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": false, "computed": true}, {"fieldname": "hsnCode", "label": "HSN/SAC", "fieldtype": "Int", "placeholder": "HSN/SAC Code"}, {"fieldname": "stockNotTransferred", "label": "Stock Not Transferred", "fieldtype": "Float", "readOnly": true}], "tableFields": ["item", "tax", "quantity", "rate", "amount"], "keywordFields": ["item", "tax"], "quickEditFields": ["item", "account", "description", "hsnCode", "tax", "rate", "transferQuantity", "transferUnit", "batch", "quantity", "unit", "unitConversionFactor", "amount", "setItemDiscountAmount", "itemDiscountAmount", "itemDiscountPercent", "itemDiscountedTotal", "itemTaxedTotal"]}