{"name": "PricingRuleItem", "label": "Pricing Rule Item", "isChild": true, "fields": [{"fieldname": "item", "label": "<PERSON><PERSON>", "fieldtype": "Link", "target": "<PERSON><PERSON>", "required": true}, {"fieldname": "unit", "label": "Unit Type", "placeholder": "Unit Type", "fieldtype": "Link", "target": "UOM", "create": true, "section": "<PERSON><PERSON><PERSON>"}], "tableFields": ["item", "unit"], "quickEditFields": ["item", "unit"]}