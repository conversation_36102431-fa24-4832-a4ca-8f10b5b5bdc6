<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18">
    <g fill="none" fill-rule="evenodd">
      <path
        fill="#92D336"
        fill-rule="nonzero"
        d="M9,0 C4.02943725,-3.04359188e-16 6.08718376e-16,4.02943725 0,9 C-6.08718376e-16,13.9705627 4.02943725,18 9,18 C13.9705627,18 18,13.9705627 18,9 C17.985583,4.03542125 13.9645788,0.0144170383 9,0 Z"
      />
      <polyline
        stroke="#FFF"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="1.5"
        points="5 9.733 7.222 12.133 13 6"
      />
    </g>
  </svg>
</template>
<script>
import Base from '../base.vue';
export default {
  extends: Base,
};
</script>
