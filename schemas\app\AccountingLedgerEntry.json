{"name": "AccountingLedgerEntry", "label": "Accounting Ledger Entry", "create": false, "isSingle": false, "isChild": false, "naming": "autoincrement", "fields": [{"label": "Entry No.", "fieldname": "name", "fieldtype": "Data", "required": true, "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "date", "label": "Date", "fieldtype": "Datetime", "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "party", "label": "Party", "fieldtype": "Link", "target": "Party", "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "account", "label": "Account", "fieldtype": "Link", "target": "Account", "required": true, "readOnly": true, "section": "<PERSON><PERSON><PERSON>"}, {"fieldname": "debit", "label": "Debit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Details"}, {"fieldname": "credit", "label": "Credit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true, "section": "Details"}, {"fieldname": "referenceType", "label": "Ref. Type", "fieldtype": "Data", "readOnly": true, "section": "Reference"}, {"fieldname": "referenceName", "label": "Ref. Name", "fieldtype": "DynamicLink", "references": "referenceType", "readOnly": true, "section": "Reference"}, {"fieldname": "reverted", "label": "Reverted", "fieldtype": "Check", "default": false, "readOnly": true, "section": "Reference"}, {"fieldname": "reverts", "label": "Reverts", "fieldtype": "Link", "target": "AccountingLedgerEntry", "readOnly": true, "section": "Reference"}], "quickEditFields": ["date", "account", "party", "debit", "credit", "referenceType", "referenceName", "reverted", "reverts"]}