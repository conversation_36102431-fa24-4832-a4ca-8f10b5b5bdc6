{"name": "PricingRuleDetail", "label": "Pricing Rule Detail", "isSingle": false, "isChild": true, "fields": [{"label": "Pricing Rule", "fieldname": "referenceName", "fieldtype": "Link", "target": "PricingRule", "readOnly": true}, {"label": "<PERSON><PERSON>", "fieldname": "referenceItem", "fieldtype": "Link", "target": "<PERSON><PERSON>", "readOnly": true}], "tableFields": ["referenceName", "referenceItem"]}